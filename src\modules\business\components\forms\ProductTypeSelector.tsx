import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, IconName } from '@/shared/components/common';
import { ProductTypeEnum } from '../../types/product.types';

interface ProductTypeOption {
  type: ProductTypeEnum;
  title: string;
  description: string;
  icon: IconName;
  color: string;
  bgColor: string;
}

interface ProductTypeSelectorProps {
  /**
   * Callback khi chọn loại sản phẩm
   */
  onTypeSelect: (type: ProductTypeEnum) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component cho phép người dùng chọn loại sản phẩm
 */
const ProductTypeSelector: React.FC<ProductTypeSelectorProps> = ({
  onTypeSelect,
  onCancel,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Đ<PERSON>nh nghĩa các loại sản phẩm với thông tin hiển thị
  const productTypes: ProductTypeOption[] = [
    {
      type: ProductTypeEnum.PHYSICAL,
      title: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
      description: t('business:product.types.physical.description', 'Sản phẩm có thể sờ được, cần vận chuyển'),
      icon: 'package',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
    },
    {
      type: ProductTypeEnum.DIGITAL,
      title: t('business:product.types.digital.title', 'Sản phẩm số'),
      description: t('business:product.types.digital.description', 'File, khóa học, ebook, phần mềm'),
      icon: 'download',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100',
    },
    {
      type: ProductTypeEnum.SERVICE,
      title: t('business:product.types.service.title', 'Dịch vụ'),
      description: t('business:product.types.service.description', 'Tư vấn, làm đẹp, bảo trì, lắp đặt'),
      icon: 'wrench',
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100',
    },
    {
      type: ProductTypeEnum.EVENT,
      title: t('business:product.types.event.title', 'Sự kiện'),
      description: t('business:product.types.event.description', 'Hội thảo, khóa học, buổi biểu diễn'),
      icon: 'calendar',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 hover:bg-orange-100',
    },
    {
      type: ProductTypeEnum.COMBO,
      title: t('business:product.types.combo.title', 'Combo'),
      description: t('business:product.types.combo.description', 'Gói sản phẩm kết hợp nhiều loại'),
      icon: 'layers',
      color: 'text-pink-600',
      bgColor: 'bg-pink-50 hover:bg-pink-100',
    },
  ];

  return (
    <div className={`w-full bg-background text-foreground ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h4" className="font-semibold mb-2">
          {t('business:product.typeSelector.title', 'Chọn loại sản phẩm')}
        </Typography>
        <Typography variant="body2" color="muted">
          {t('business:product.typeSelector.description', 'Chọn loại sản phẩm phù hợp để tạo form tương ứng')}
        </Typography>
      </div>

      {/* Product Type Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
        {productTypes.map((option) => (
          <Card
            key={option.type}
            variant="bordered"
            hoverable
            className={`
              cursor-pointer
              transition-all
              duration-300
              ease-in-out
              hover:shadow-lg
              hover:shadow-primary/10
              hover:-translate-y-1
              border-2
              hover:border-primary/30
              ${option.bgColor}
              dark:bg-gray-800
              dark:hover:bg-gray-700
            `}
            onClick={() => onTypeSelect(option.type)}
          >
            <div className="p-6 text-center">
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${option.bgColor.replace('hover:', '')}`}>
                <Icon 
                  name={option.icon} 
                  size="xl" 
                  className={`${option.color} dark:text-white`}
                />
              </div>

              {/* Title */}
              <Typography variant="h6" className="font-semibold mb-2">
                {option.title}
              </Typography>

              {/* Description */}
              <Typography variant="body2" color="muted" className="text-sm">
                {option.description}
              </Typography>
            </div>
          </Card>
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-end">
        <Card
          variant="bordered"
          hoverable
          className="cursor-pointer transition-all duration-200 hover:shadow-md"
          onClick={onCancel}
        >
          <div className="px-6 py-3 flex items-center space-x-2">
            <Icon name="x" size="sm" className="text-gray-500" />
            <Typography variant="body2" className="font-medium">
              {t('common:cancel', 'Hủy')}
            </Typography>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ProductTypeSelector;
